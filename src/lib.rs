use actix_web::{HttpRequest, dev::ConnectionInfo, web};
use anyhow::anyhow;
use chrono::prelude::*;
use jsonwebtoken::{Algorithm, DecodingKey, Validation, decode};
use lemonsqueezy::LemonSqueezy;
use lettre::{
    SmtpTransport,
    transport::smtp::{authentication::Credentials, client::TlsParameters},
};
use sea_orm::{ColumnTrait, DatabaseConnection, EntityTrait, QueryFilter, Set, TryIntoModel};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

use crate::util::entities::{
    account_user_groups, api_keys, prelude::*, quadrant_notifications, user_groups,
};

pub mod util;

pub const DEFAULT_SHARE_LIMIT: i64 = env!("DEFAULT_SHARE_LIMIT").unwrap_or_else(10);
pub const DEFAULT_SYNC_LIMIT: i64 = 2;
pub const DEFAULT_QUADRANT_ICON: &str =
    "https://github.com/mrquantumoff/quadrant/raw/refs/heads/next/public/logoNoBg.svg";

#[derive(Debug, Serialize, Deserialize)]
pub struct Claims {
    pub sub: String,
    pub exp: i64,
    pub iss: String,
    pub iat: i64,
    pub cntry: String,
    pub device: String,
    pub uid: String,
    pub ip: String,
    pub enforce_ip: Option<bool>,
}
pub struct ApiKeySearchFilter {
    pub product: String,
    pub scope: String,
}
impl Claims {
    pub fn from_jwt(token: String, device: ConnectionInfo) -> Result<Claims, anyhow::Error> {
        let device = device.realip_remote_addr();
        if device.is_none() {
            return Err(anyhow::Error::msg("Failed to get the device address"));
        }
        let ip = device.unwrap().to_string();
        let mut validation = Validation::new(Algorithm::HS256);
        validation.set_issuer(&["MrQuantumOFF.DEV API"]);
        validation.set_required_spec_claims(&["exp", "cntry", "ip", "sub", "iat", "device", "uid"]);
        let jwt_secret = std::env::var("JWT_SECRET")?;

        let decoded_token = decode::<Claims>(
            &token,
            &DecodingKey::from_secret(jwt_secret.as_ref()),
            &validation,
        )?;

        let now = Utc::now().timestamp();
        if decoded_token.claims.iat > now {
            return Err(anyhow::Error::msg("Token isn't in effect yet."));
        };
        if decoded_token.claims.exp < now {
            return Err(anyhow::Error::msg("Token has expired."));
        };

        let enforce_ip = decoded_token.claims.enforce_ip.unwrap_or(true);

        if decoded_token.claims.ip != ip && ip != "127.0.0.1" && enforce_ip {
            return Err(anyhow::Error::msg("Token IP doesn't match."));
        };

        Ok(decoded_token.claims)
    }
}

#[derive(Debug, Clone)]
pub struct WebData {
    pub discord_verify_oauth2: OAuth2App,
    pub smtp: SMTPCredentials,
    pub conn: DatabaseConnection,
    pub lemon: LemonSqueezyData,
}

#[derive(Debug, Clone)]
pub struct LemonSqueezyData {
    pub webhook_secret: String,
    pub plus_sub_id: i64,
    pub store_id: i64,
    pub plus_sub_gid: String,
    pub ls_instance: LemonSqueezy,
    pub api_key: String,
}

#[derive(Debug, Clone)]
pub struct OAuth2App {
    pub client_id: String,
    pub client_secret: String,
    pub redirect_uri: String,
}

#[derive(Debug, Clone)]
pub struct SMTPCredentials {
    pub username: String,
    pub password: String,
    pub url: String,
}

pub fn build_mailer(creds: Credentials, url: String) -> SmtpTransport {
    SmtpTransport::relay(&url)
        .unwrap()
        .tls(lettre::transport::smtp::client::Tls::Required(
            TlsParameters::builder(url)
                .dangerous_accept_invalid_certs(true)
                .build()
                .unwrap(),
        ))
        .credentials(creds)
        .build()
}

pub async fn get_api_keys(
    data: WebData,
    filter: ApiKeySearchFilter,
) -> Result<Vec<String>, anyhow::Error> {
    let api_keys = ApiKeys::find()
        .filter(api_keys::Column::Purpose.eq(filter.product))
        .all(&data.conn)
        .await?;

    let mut keys_string: Vec<String> = Vec::new();
    for key in api_keys {
        if key.scope.contains(&filter.scope) {
            keys_string.push(key.apikey)
        }
    }
    println!("{:?}", keys_string);
    Ok(keys_string)
}
pub fn verify_jwt(request: HttpRequest) -> Result<Claims, anyhow::Error> {
    let token = if request.headers().get("Authorization").is_some() {
        let token = request
            .headers()
            .get("Authorization")
            .unwrap()
            .to_str()?
            .to_string()
            .split("Bearer ")
            .collect::<Vec<&str>>()
            .last()
            .unwrap_or(&"")
            .to_string();
        token
    } else if request.cookie("quadrant_id_token").is_some() {
        request
            .cookie("quadrant_id_token")
            .unwrap()
            .value()
            .to_string()
    } else {
        return Err(anyhow!("No JWT token provided"));
    };

    let device = request.connection_info();

    let device = device.realip_remote_addr();
    if device.is_none() {
        return Err(anyhow::Error::msg("Failed to get the device address"));
    }
    let ip = device.unwrap().to_string();
    let mut validation = Validation::new(Algorithm::HS256);
    validation.set_issuer(&["MrQuantumOFF.DEV API"]);
    validation.set_required_spec_claims(&["exp", "cntry", "ip", "sub", "iat", "device", "uid"]);
    let jwt_secret = std::env::var("JWT_SECRET")?;

    let decoded_token = decode::<Claims>(
        &token,
        &DecodingKey::from_secret(jwt_secret.as_ref()),
        &validation,
    )?;

    let now = Utc::now().timestamp();
    if decoded_token.claims.iat > now {
        return Err(anyhow::Error::msg("Token isn't in effect yet."));
    };
    if decoded_token.claims.exp < now {
        return Err(anyhow::Error::msg("Token has expired."));
    };

    let enforce_ip = decoded_token.claims.enforce_ip.unwrap_or(true);

    if decoded_token.claims.ip != ip && ip != "127.0.0.1" && enforce_ip {
        return Err(anyhow::Error::msg("Token IP doesn't match."));
    };

    Ok(decoded_token.claims)
}
pub async fn send_notification(
    user_id: String,
    notification: impl Serialize,
    data: web::Data<WebData>,
) -> Result<quadrant_notifications::Model, anyhow::Error> {
    let notification_str = serde_json::to_string(&notification).unwrap();

    // Create SeaORM model
    let model = quadrant_notifications::ActiveModel {
        created_at: Set(Utc::now().into()),
        user_id: Set(user_id.clone()),
        notification_id: Set(Uuid::now_v7().to_string()),
        message: Set(notification_str),
        read: Set(false),
        ..Default::default()
    };

    // Insert notification
    QuadrantNotifications::insert(model.clone())
        .exec(&data.conn)
        .await
        .map_err(|e| anyhow!("Failed to send notification: {}", e))?;

    Ok(model.try_into_model()?)
}

pub async fn get_share_sync_limit_and_groups(
    account_id: String,
    conn: &DatabaseConnection,
) -> (Vec<user_groups::Model>, i64, i64) {
    let mut share_limit = DEFAULT_SHARE_LIMIT;
    let mut sync_limit = DEFAULT_SYNC_LIMIT;

    // Get user groups using SeaORM
    let groups = AccountUserGroups::find()
        .filter(account_user_groups::Column::AccountId.eq(account_id))
        .find_also_related(UserGroups)
        .all(conn)
        .await
        .unwrap_or_default();

    let mut groups = groups
        .into_iter()
        .filter(|(_, group)| group.is_some())
        .map(|(_, group)| group.unwrap())
        .collect::<Vec<_>>();

    groups.sort_by(|a, b| a.priority.cmp(&b.priority));

    for group in groups.clone() {
        if group.privileges.contains(&"proShareSync".to_string()) {
            share_limit = 200;
            sync_limit = 250;
            break;
        }
        if group.privileges.contains(&"ogShareSync".to_string()) {
            share_limit = 75;
            sync_limit = 100;
            break;
        }
        if group.privileges.contains(&"plusShareSync".to_string()) {
            share_limit = 25;
            sync_limit = 15;
            break;
        }
    }

    (groups, share_limit, sync_limit)
}

pub fn generate_email_template(title: &str, message: &str, icon: &str) -> String {
    let mut notification_html = include_str!("../notification.html").to_string();
    notification_html = notification_html
        .replace("%NOTIFICATION_TITLE%", title)
        .replace("%NOTIFICATION_MESSAGE%", &message)
        .replace("%NOTIFICATION_ICON%", &icon);
    notification_html
}
